.container {
    width: 100%;
    margin: 48px 0;
    position: relative;
    z-index: 1;
}

.studiosGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    width: 100%;
}

.loading, .error, .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #fff;
    font-family: 'Inter', sans-serif;
}

.loading p, .error p, .empty p {
    font-size: 16px;
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(73, 99, 138, 0.3);
    border-top: 3px solid #49638A;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.retryButton {
    margin-top: 16px;
    padding: 12px 24px;
    background: #49638A;
    color: white;
    border: none;
    border-radius: 8px;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.retryButton:hover {
    background: #5a7bb5;
    transform: translateY(-1px);
}

.retryButton:active {
    transform: translateY(0);
}

/* Адаптивність */
@media (max-width: 1200px) {
    .studiosGrid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 900px) {
    .studiosGrid {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 16px 0;
    }

    .studiosGrid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .loading, .error, .empty {
        padding: 40px 16px;
    }
}

@media (max-width: 480px) {
    .studiosGrid {
        gap: 12px;
    }
    
    .loading, .error, .empty {
        padding: 30px 12px;
    }
    
    .loading p, .error p, .empty p {
        font-size: 14px;
    }
    
    .retryButton {
        padding: 10px 20px;
        font-size: 13px;
    }
}
