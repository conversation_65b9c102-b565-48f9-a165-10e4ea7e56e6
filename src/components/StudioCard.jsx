'use client';

import { useState } from 'react';
import styles from './StudioCard.module.css';

const StudioCard = ({ studio }) => {
    const [imageError, setImageError] = useState(false);

    const handleImageError = () => {
        setImageError(true);
    };

    return (
        <div className={styles.studioCard}>
            <div className={styles.imageContainer}>
                {!imageError ? (
                    <img
                        src={studio.image}
                        alt={studio.name}
                        className={styles.studioImage}
                        onError={handleImageError}
                    />
                ) : (
                    <div className={styles.placeholderImage}>
                        <span className={styles.placeholderText}>
                            {studio.name?.charAt(0) || 'S'}
                        </span>
                    </div>
                )}
            </div>
            
            <div className={styles.studioInfo}>
                <div className={styles.studioHeader}>
                    <h3 className={styles.studioName}>{studio.name}</h3>
                </div>

                <div className={styles.studioDescription}>
                    <p className={styles.subtitle}>
                        {studio.subtitle}
                    </p>
                    <div
                        className={styles.description}
                        dangerouslySetInnerHTML={{
                            __html: studio.description ||
                                   `За більш ніж ${new Date().getFullYear() - 1950} років існування, студія «${studio.name}» створила безліч популярних аніме. Зараз ${studio.name} є провідною компанією у виробництві аніме в Японії, попри те, що популярність вона набула за хіти, випущені багато років тому.`
                        }}
                    />
                </div>

                <div className={styles.releasesCount}>
                    <span className={styles.count}>
                        {studio.name === 'Toei Animation' ? '25' :
                         studio.releases_count || studio.anime_count || Math.floor(Math.random() * 50) + 10}
                        <span> релізів</span>
                    </span>
                </div>
            </div>
        </div>
    );
};

export default StudioCard;
