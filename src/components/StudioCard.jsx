'use client';

import { useState } from 'react';
import styles from './StudioCard.module.css';

const StudioCard = ({ studio }) => {
    const [imageError, setImageError] = useState(false);

    const handleImageError = () => {
        setImageError(true);
    };

    return (
        <div className={styles.studioCard}>
            <div className={styles.imageContainer}>
                {!imageError ? (
                    <img 
                        src={studio.logo || studio.image || '/default-studio.png'} 
                        alt={studio.name}
                        className={styles.studioImage}
                        onError={handleImageError}
                    />
                ) : (
                    <div className={styles.placeholderImage}>
                        <span className={styles.placeholderText}>
                            {studio.name?.charAt(0) || 'S'}
                        </span>
                    </div>
                )}
            </div>
            
            <div className={styles.studioInfo}>
                <h3 className={styles.studioName}>{studio.name}</h3>
                
                <div className={styles.studioDescription}>
                    <p className={styles.subtitle}>
                        {studio.country && `${studio.country} анімаційна студія`}
                    </p>
                    <p className={styles.description}>
                        {studio.founded && `Заснована ${studio.founded} року. `}
                        {studio.description || 
                         `За більш ніж ${new Date().getFullYear() - (studio.founded || 1950)} років існування, студія «${studio.name}» створила безліч популярних аніме. Зараз ${studio.name} є провідною компанією у виробництві аніме в Японії, попри те, що популярність вона набула за хіти, випущені багато років тому.`}
                    </p>
                </div>
                
                <div className={styles.releasesCount}>
                    <span className={styles.count}>{studio.releases_count || studio.anime_count || 25}</span>
                    <span className={styles.label}>релізів</span>
                </div>
            </div>
        </div>
    );
};

export default StudioCard;
