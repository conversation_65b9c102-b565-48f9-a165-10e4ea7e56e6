/* CSS змінні для відступів тестових компонентів */
:root {
    --container-padding: 84px;
    --test-offset: 20px;
    --test-left-position: calc(var(--container-padding) + var(--test-offset)); /* 104px */
    --test-right-position: calc(var(--container-padding) + var(--test-offset)); /* 104px */
}

/* Адаптивні відступи для планшетів */
@media (max-width: 768px) {
    :root {
        --container-padding: 32px;
        --test-left-position: calc(var(--container-padding) + var(--test-offset)); /* 52px */
        --test-right-position: calc(var(--container-padding) + var(--test-offset)); /* 52px */
    }
}

/* Адаптивні відступи для мобільних */
@media (max-width: 600px) {
    :root {
        --container-padding: 16px;
        --test-left-position: calc(var(--container-padding) + var(--test-offset)); /* 36px */
        --test-right-position: calc(var(--container-padding) + var(--test-offset)); /* 36px */
    }
}

/* Стилі для тестових компонентів */
.testControlsButton {
    position: fixed;
    top: 120px;
    left: var(--test-left-position);
    background: #49638A;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    z-index: 1002;
}

.testControlsPanel {
    position: fixed;
    top: 160px;
    left: var(--test-left-position);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 1002;
    border: 1px solid #49638A;
    min-width: 150px;
}

.responsiveButton {
    position: fixed;
    bottom: 80px;
    left: var(--test-left-position);
    background: #49638A;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    z-index: 1001;
}

.responsivePanel {
    position: fixed;
    bottom: 80px;
    left: var(--test-left-position);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 16px;
    border-radius: 12px;
    font-size: 12px;
    z-index: 1001;
    min-width: 250px;
    border: 1px solid #49638A;
}

.authTestButton {
    position: fixed;
    bottom: 20px;
    right: var(--test-right-position);
    background: #49638A;
    color: white;
    padding: 10px;
    border-radius: 8px;
    z-index: 1000;
    max-width: 200px;
}
