'use client';

import { useAuth } from '@/contexts/AuthContext';

const AuthTestButton = () => {
    const { isAuthenticated, login, logout, user } = useAuth();

    const handleTestLogin = () => {
        const testUser = {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>'
        };
        login(testUser);
    };

    return (
        <div style={{ 
            position: 'fixed', 
            bottom: '20px', 
            left: '20px', 
            background: '#49638A', 
            color: 'white', 
            padding: '10px', 
            borderRadius: '8px',
            zIndex: 1000
        }}>
            <p>Auth Status: {isAuthenticated ? 'Logged In' : 'Not Logged In'}</p>
            {user && <p>User: {user.name}</p>}
            {isAuthenticated ? (
                <button onClick={logout} style={{ 
                    background: '#ff4444', 
                    color: 'white', 
                    border: 'none', 
                    padding: '5px 10px', 
                    borderRadius: '4px',
                    cursor: 'pointer'
                }}>
                    Logout
                </button>
            ) : (
                <button onClick={handleTestLogin} style={{ 
                    background: '#44ff44', 
                    color: 'white', 
                    border: 'none', 
                    padding: '5px 10px', 
                    borderRadius: '4px',
                    cursor: 'pointer'
                }}>
                    Test Login
                </button>
            )}
        </div>
    );
};

export default AuthTestButton;
