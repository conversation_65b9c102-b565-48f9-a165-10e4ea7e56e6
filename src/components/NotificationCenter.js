'use client';

import React, { useState } from 'react';
import styles from './NotificationCenter.module.css';

const NotificationCenter = () => {
    const [isOpen, setIsOpen] = useState(false);

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className={styles.notificationContainer}>
            <button onClick={toggleDropdown} className={styles.bellButton}>
                <img src="/tabler-icon-bell-ringing-filled.svg" alt="Bell" />
            </button>
            {isOpen && (
                <div className={styles.dropdown}>
                    <h3>Notifications</h3>
                    <ul>
                        <li>New message received at 08:15 PM CEST</li>
                        <li>Update available at 07:50 PM CEST</li>
                        <li>System alert at 06:30 PM CEST</li>
                    </ul>
                </div>
            )}
        </div>
    );
};

export default NotificationCenter;