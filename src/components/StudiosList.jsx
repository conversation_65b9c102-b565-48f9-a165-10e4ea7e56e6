'use client';

import { useState, useEffect } from 'react';
import StudioCard from './StudioCard';
import styles from './StudiosList.module.css';

const StudiosList = ({ sortBy, filterBy }) => {
    const [studios, setStudios] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchStudios();
    }, []);

    useEffect(() => {
        if (studios.length > 0) {
            const sortedAndFiltered = sortAndFilterStudios(studios, sortBy, filterBy);
            setStudios(sortedAndFiltered);
        }
    }, [sortBy, filterBy]);

    const fetchStudios = async () => {
        try {
            setLoading(true);
            const response = await fetch('http://localhost:3002/studies');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('Fetched studios:', data);
            
            // JSON Server повертає дані прямо як масив
            const studiosArray = Array.isArray(data) ? data : [];
            
            setStudios(studiosArray);
        } catch (err) {
            console.error('Error fetching studios:', err);
            setError(err.message);
            
            // Fallback дані для демонстрації
            setStudios([
                {
                    id: 1,
                    name: 'Toei Animation',
                    country: 'Японська',
                    subtitle: 'Японська анімаційна студія',
                    founded: 1956,
                    description: 'За більш ніж 50 років існування, студія «Toei Animation» створила безліч популярних аніме. Зараз Toei є провідною компанією у виробництві аніме в Японії, попри те, що популярність вона набула за хіти, випущені багато років тому.',
                    releases_count: 25,
                    logo: '/toei-logo.png'
                }
            ]);
        } finally {
            setLoading(false);
        }
    };

    const sortAndFilterStudios = (studiosArray, sort, filter) => {
        let filtered = [...studiosArray];

        // Фільтрування
        switch (filter) {
            case 'few_releases':
                filtered = filtered.filter(studio => (studio.releases_count || studio.anime_count || 0) <= 10);
                break;
            case 'medium_releases':
                filtered = filtered.filter(studio => {
                    const count = studio.releases_count || studio.anime_count || 0;
                    return count > 10 && count <= 50;
                });
                break;
            case 'many_releases':
                filtered = filtered.filter(studio => (studio.releases_count || studio.anime_count || 0) > 50);
                break;
            case 'active':
                filtered = filtered.filter(studio => studio.status === 'active' || studio.active === true);
                break;
            case 'popular':
                filtered = filtered.filter(studio => studio.popular === true || (studio.releases_count || 0) > 20);
                break;
            default:
                break;
        }

        // Сортування
        switch (sort) {
            case 'name_asc':
                filtered.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
                break;
            case 'name_desc':
                filtered.sort((a, b) => (b.name || '').localeCompare(a.name || ''));
                break;
            case 'popularity_desc':
                filtered.sort((a, b) => (b.releases_count || b.anime_count || 0) - (a.releases_count || a.anime_count || 0));
                break;
            case 'rating_desc':
                filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
                break;
            case 'year_desc':
                filtered.sort((a, b) => (b.founded || 0) - (a.founded || 0));
                break;
            case 'year_asc':
                filtered.sort((a, b) => (a.founded || 0) - (b.founded || 0));
                break;
            default:
                break;
        }

        return filtered;
    };

    if (loading) {
        return (
            <div className={styles.container}>
                <div className={styles.loading}>
                    <div className={styles.spinner}></div>
                    <p>Завантаження студій...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={styles.container}>
                <div className={styles.error}>
                    <p>Помилка завантаження: {error}</p>
                    <button onClick={fetchStudios} className={styles.retryButton}>
                        Спробувати знову
                    </button>
                </div>
            </div>
        );
    }

    if (studios.length === 0) {
        return (
            <div className={styles.container}>
                <div className={styles.empty}>
                    <p>Студії не знайдено</p>
                </div>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <div className={styles.studiosGrid}>
                {studios.map((studio, index) => (
                    <StudioCard 
                        key={studio.id || index} 
                        studio={studio} 
                    />
                ))}
            </div>
        </div>
    );
};

export default StudiosList;
