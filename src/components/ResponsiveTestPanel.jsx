'use client';

import { useState, useEffect } from 'react';

const ResponsiveTestPanel = () => {
    const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const updateSize = () => {
            setWindowSize({
                width: window.innerWidth,
                height: window.innerHeight
            });
        };

        updateSize();
        window.addEventListener('resize', updateSize);
        return () => window.removeEventListener('resize', updateSize);
    }, []);

    const getBreakpointInfo = () => {
        const width = windowSize.width;
        if (width >= 1200) return { name: 'Desktop', color: '#4CAF50' };
        if (width >= 900) return { name: 'Edge Vertical Tabs', color: '#FF9800' };
        if (width >= 600) return { name: 'Tablet', color: '#2196F3' };
        return { name: 'Mobile', color: '#F44336' };
    };

    const breakpoint = getBreakpointInfo();

    const testSizes = [
        { name: 'Desktop Full', width: 1920, height: 1080 },
        { name: 'Edge Vertical Tabs', width: 1000, height: 800 },
        { name: 'Edge Compact', width: 800, height: 600 },
        { name: 'Tablet', width: 768, height: 1024 },
        { name: 'Mobile', width: 375, height: 667 }
    ];

    const simulateSize = (width, height) => {
        // Це лише для демонстрації - в реальності розмір вікна змінюється браузером
        alert(`Симуляція розміру: ${width}x${height}px\nВідкрийте DevTools і змініть розмір вікна для тестування`);
    };

    if (!isVisible) {
        return (
            <button
                onClick={() => setIsVisible(true)}
                style={{
                    position: 'fixed',
                    bottom: '80px',
                    left: '20px',
                    background: '#49638A',
                    color: 'white',
                    border: 'none',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '12px',
                    zIndex: 1001
                }}
            >
                📱 Responsive Test
            </button>
        );
    }

    return (
        <div style={{
            position: 'fixed',
            bottom: '80px',
            left: '104px', // 84px padding + 20px відступ
            background: 'rgba(0, 0, 0, 0.9)',
            color: 'white',
            padding: '16px',
            borderRadius: '12px',
            fontSize: '12px',
            zIndex: 1001,
            minWidth: '250px',
            border: '1px solid #49638A'
        }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                <h4 style={{ margin: 0, fontSize: '14px' }}>📱 Responsive Test</h4>
                <button
                    onClick={() => setIsVisible(false)}
                    style={{
                        background: 'transparent',
                        color: 'white',
                        border: 'none',
                        cursor: 'pointer',
                        fontSize: '16px'
                    }}
                >
                    ✕
                </button>
            </div>
            
            <div style={{ marginBottom: '12px' }}>
                <div style={{ marginBottom: '8px' }}>
                    <strong>Current Size:</strong> {windowSize.width} × {windowSize.height}px
                </div>
                <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '8px',
                    marginBottom: '8px'
                }}>
                    <div style={{
                        width: '12px',
                        height: '12px',
                        borderRadius: '50%',
                        background: breakpoint.color
                    }}></div>
                    <strong>{breakpoint.name}</strong>
                </div>
            </div>

            <div style={{ marginBottom: '12px' }}>
                <strong>Test Sizes:</strong>
            </div>
            
            {testSizes.map((size, index) => (
                <button
                    key={index}
                    onClick={() => simulateSize(size.width, size.height)}
                    style={{
                        display: 'block',
                        width: '100%',
                        background: 'rgba(73, 99, 138, 0.3)',
                        color: 'white',
                        border: '1px solid #49638A',
                        padding: '6px 8px',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        marginBottom: '4px',
                        fontSize: '11px',
                        textAlign: 'left'
                    }}
                >
                    {size.name}: {size.width}×{size.height}
                </button>
            ))}

            <div style={{ 
                marginTop: '12px', 
                padding: '8px', 
                background: 'rgba(73, 99, 138, 0.2)', 
                borderRadius: '6px',
                fontSize: '10px'
            }}>
                💡 Tip: Відкрийте DevTools (F12) → Toggle device toolbar для тестування різних розмірів
            </div>
        </div>
    );
};

export default ResponsiveTestPanel;
