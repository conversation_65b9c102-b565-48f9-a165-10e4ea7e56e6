'use client';

import React, { useState } from 'react';
import styles from './SearchBar.module.css';

const SearchBar = () => {
    const [query, setQuery] = useState('');

    const handleSearch = (e) => {
        setQuery(e.target.value);
        // Add your search logic here, e.g., filter data or make an API call
        console.log('Search query:', e.target.value);
    };

    return (
        <div className={styles.searchContainer}>
            <span className={styles.icon}>
                <img src="/tabler-icon-search.svg" />
            </span>
            <input
                type="text"
                placeholder="Поиск"
                value={query}
                onChange={handleSearch}
                className={styles.searchInput}
            />
        </div>
    );
};

export default SearchBar;