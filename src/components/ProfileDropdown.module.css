.dropdown {
    position: relative;
    display: inline-block;
}

.profileImage {
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
}

.menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    list-style: none;
    padding: 0;
    margin: 8px 0 0 0;
    transition: opacity 0.2s ease, transform 0.2s ease;
    z-index: 1000;
    max-height: 200px;
    overflow: hidden;
    min-width: 200px;
}

.menuItem {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

/*ul {*/
/*    background: black;*/
/*}*/

.li_profile_name {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    background: none;
    border: none;
    width: 100%;
}

.profileName {
    margin: 0;
    font-size: 16px;
    white-space: nowrap; /* Запобігає перенесенню імені */
    max-width: 240px;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333;
}

.menuItem:hover,
.li_profile_name:hover {
    background-color: #f5f5f5;
}

/* Анімація появи меню */
.menu {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
}

.dropdown[aria-expanded="true"] .menu {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Мобільна адаптивність */
@media (max-width: 768px) {
    .menu {
        right: -10px;
    }
    .profileName {
        font-size: 14px;
        max-width: 180px;
    }
}