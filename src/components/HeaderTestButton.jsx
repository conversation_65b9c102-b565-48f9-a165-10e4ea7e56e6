'use client';

const HeaderTestButton = () => {
    const handleTestClick = () => {
        console.log('Test button clicked');
        // Можна додати будь-яку логіку тестування тут
    };

    return (
        <button
            onClick={handleTestClick}
            style={{
                background: 'rgba(73, 99, 138, 0.3)',
                color: 'white',
                border: '1px solid #49638A',
                borderRadius: '6px',
                padding: '8px 12px',
                cursor: 'pointer',
                fontSize: '12px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: '500',
                transition: 'all 0.3s ease',
                whiteSpace: 'nowrap'
            }}
            onMouseEnter={(e) => {
                e.target.style.background = 'rgba(73, 99, 138, 0.5)';
            }}
            onMouseLeave={(e) => {
                e.target.style.background = 'rgba(73, 99, 138, 0.3)';
            }}
        >
            🔧 Test
        </button>
    );
};

export default HeaderTestButton;
