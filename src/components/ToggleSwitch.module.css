/* components/ToggleSwitch.module.css */
.switch {
    position: relative;
    display: inline-block;
    width: 42px; /* Бул<PERSON> 60px, зменшено на 30% */
    height: 24px; /* Було 34px, зменшено на 30% */
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333439;
    transition: 0.4s;
    border-radius: 24px; /* Зменшено відповідно до висоти */
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px; /* Було 26px, зменшено на 30% */
    width: 18px; /* Було 26px, зменшено на 30% */
    left: 3px; /* Було 4px, зменшено пропорційно */
    bottom: 3px; /* Було 4px, зменшено пропорційно */
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:checked + .slider:before {
    transform: translateX(18px); /* Було 26px, зменшено на 30% */
}