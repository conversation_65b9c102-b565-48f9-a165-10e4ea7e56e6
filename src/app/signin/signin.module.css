.container {
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
}

.line5 {
  position: absolute;
  right: 1467px;
  bottom: 353px;
}

.line6 {
  position: absolute;
  left: 1820px;
  bottom: 155.69px;
}

.line7 {
  position: absolute;
  left: 1400px;
  top: 455px;
  transform: rotate(170deg);
}

.line8 {
  position: absolute;
  left: 1640px;
}

.line9 {
  position: absolute;
  right: 1670px;
}

.line10 {
  position: absolute;
  right: 1185.52px;
  top: 657px;
}

.card {
  /*background: #000;*/
  padding: 55px;
  border-radius: 64px;
  /*box-shadow: 0 0 15px rgba(255, 0, 255, 0.5);*/
  text-align: center;
  width: 584px;
  height: 780px;
}

.logo {
  width: 120px;
  margin: 0 auto;
  border-radius: 10px;
}

.login {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 36px;
    margin-bottom: 42px;
}

.title {
    color: #FFF;
    text-align: center;
    /*text-shadow: 0px 4px 30px rgba(255, 0, 199, 0.50);*/
    font-family: Inter;
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.inputGroup {
    margin-bottom: 24px;
}

.inputContainer {
  position: relative;
}

.Icon {
  position: absolute;
  width: 24px;
  height: 24px;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  aspect-ratio: 1/1;
}

.customInput {
  width: 100%;
  padding: 15px 32px;
  padding-left: 50px;
  font-size: 16px;
  color: white;
  /*background: black;*/
  border: 1px solid #4963BA;
  border-radius: 52px;
  outline: none;
  transition: 0.3s ease-in-out;
}

.customInput:focus {
  border-color: #5878E1;
  box-shadow: 0 0 10px #4963BA;
}

.customInput::placeholder {
  color: rgba(255, 255, 255, 1);
}

.rememberMe {
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    font-weight: 400;
    color: #FFF;
    margin-top: 12px;
}

.checkboxContainer {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 15px;
    
}

.checkboxContainer input {
    display: none;
}

.checkmark {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background-color: transparent; 
  border: 2px solid #49638A;
  display: inline-block;
  position: relative;
  margin-right: 10px;
  transition: 0.3s;
}

.checkboxContainer input:checked + .checkmark {
  background-color: #49638A;
  border-color: #49638A;
}

.checkboxContainer input:checked + .checkmark::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 2px;
    width: 7px;
    height: 14px;
    border: solid #000;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgotPassword {
    color: #fff;
    font-style: normal;
    font-weight: 400;
    line-height: normal;  
  }
  
  .button {
    height: 60px;
    padding: 0px 42px;
    background: #49638A;
    color: white;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    border-radius: 52px;
    cursor: pointer;
    margin-bottom: 32px;
  } 
  
  .register {
    margin-top: 24px;
    color: #49638A;
    text-align: center;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  
  .link {
    color: #fff;
    text-decoration: underline;
    text-align: center;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  
  .socialText {
    color: #fff;
    margin-top: 24px;
    font-size: 18px;
    text-align: center;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
  
  .socialIcons {
    display: flex;
    margin-top: 24px;
    justify-content: center;
    gap: 72px;
  }
  
  .icon {
    display: flex;
    width: 72px;
    height: 72px;
    justify-content: center;
    align-items: center;
    aspect-ratio: 1/1;
    border-radius: 53px;
    background: #49638A;
    padding: 9px;
  }

  .iconImage {
      max-width: none;
  }

.error {
    color: red;
    font-size: 14px;
    margin: 10px 0;
    text-align: center;
}