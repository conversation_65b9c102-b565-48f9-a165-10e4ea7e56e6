.container {
    display: flex;
    position: relative;
    justify-content: center;
    /*align-items: center;*/
    height: 100vh;
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
    color: #e0e0e0;
}

.line5 {
    position: absolute;
    right: 1467px;
    bottom: 353px;
}

.line6 {
    position: absolute;
    left: 1777px;
    bottom: 155.69px;
}

.line7 {
    position: absolute;
    left: 1288px;
    top: 455px;
}

.line8 {
    position: absolute;
    left: 1604.87px;
}

.line9 {
    position: absolute;
    right: 1670px;
}

.line10 {
    position: absolute;
    right: 1185.52px;
    top: 657px;
}

.header_settings {
    display: flex;
    width: 1920px;
    height: 100px;
    padding: 24px 82px;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.01);
}
