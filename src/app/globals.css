@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
/*@import "tailwindcss";*/


html, body {
  font-family: 'Inter', sans-serif;
  font-style: normal;
  line-height: normal;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%), #000;
  /*background-color: #000;  */
  /*background-image: url('/Slide 16_9 - 1.png');*/
}

.container {
  display: flex;
  position: relative;
  height: 100vh;
  max-width: 100vw;
}

.header_main_page {
  display: flex;
  width: 1920px;
  height: 100px;
  padding: 24px 82px;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.01);
}

.header_buttons {
  display: flex;
  gap: 28px;
  color: #fff;
  position: absolute;
  top: 24px;
  right: 82px;
  justify-content: flex-end;
  /*width: 100%;*/
  /*flex-direction: column;*/
}

.header_button_login {
  border-radius: 18px;
  border: 1px solid #49638A;
  background: rgba(0, 0, 0, 0.02);
  backdrop-filter: blur(3.25px);
  color: #fff;
  font-size: clamp(14px, 1.5vw, 16px);
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  padding: clamp(12px, 1.5vh, 15.5px) clamp(20px, 4vw, 50px);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.header_button_login:hover {
  background: rgba(73, 99, 138, 0.2);
  transform: translateY(-1px);
}

.header_button_signup {
  border-radius: 18px;
  border: 1px solid #49638A;
  background: #49638A;
  color: #fff;
  font-size: clamp(14px, 1.5vw, 16px);
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  padding: clamp(12px, 1.5vh, 15.5px) clamp(20px, 3vw, 31px);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.header_button_signup:hover {
  background: #5a7bb5;
  transform: translateY(-1px);
}
