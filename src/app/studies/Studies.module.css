.container {
    display: flex;
    position: relative;
    flex-direction: column;
    padding-left: 64px;
    padding-right: 64px;
    height: 100vh;
    width: 100vw;
    max-width: 100%;
    overflow: hidden;
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
    z-index: 0;
}

/* Декоративні лінії - адаптивні позиції */
.line5 {
    position: absolute;
    right: clamp(100px, 15vw, 300px);
    bottom: clamp(200px, 30vh, 353px);
    z-index: 1;
    opacity: 0.8;
}

.line6 {
    position: absolute;
    right: clamp(50px, 8vw, 200px);
    bottom: clamp(100px, 15vh, 155px);
    z-index: 1;
    opacity: 0.8;
}

.line7 {
    position: absolute;
    left: clamp(100px, 20vw, 400px);
    top: clamp(300px, 40vh, 455px);
    z-index: 1;
    opacity: 0.8;
}

.line8 {
    position: absolute;
    left: clamp(200px, 25vw, 500px);
    top: clamp(50px, 10vh, 100px);
    z-index: 1;
    opacity: 0.8;
}

.line9 {
    position: absolute;
    right: clamp(150px, 20vw, 400px);
    top: clamp(100px, 15vh, 200px);
    z-index: 1;
    opacity: 0.8;
}

.line10 {
    position: absolute;
    right: clamp(80px, 12vw, 250px);
    top: clamp(400px, 50vh, 657px);
    z-index: 1;
    opacity: 0.8;
}

/* Приховуємо деякі декоративні елементи на малих екранах */
@media (max-width: 900px) {
    .line5, .line6, .line7 {
        display: none;
    }
}

@media (max-width: 600px) {
    .line8, .line9, .line10 {
        display: none;
    }
}

.header_studies {
    display: flex;
    width: 100%;
    max-width: 100vw;
    padding: 24px clamp(8px, 2vw, 32px);
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.01);
    backdrop-filter: blur(4.449999809265137px);
    z-index: 2;
    box-sizing: border-box;
}

.logo {
    width: 50px;
    height: 50px;
}

.searchBar {
    display: flex;
    align-items: center;
    width: clamp(200px, 25vw, 326px);
    height: 55px;
    z-index: 2;
}

.notificationCenter {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.header_buttons_studies {
    display: flex;
    gap: clamp(12px, 2vw, 24px);
    color: #fff;
    justify-content: flex-end;
    align-items: center;
    z-index: 2;
    flex-shrink: 0;
}

.header_profile_dropdown {
    display: flex;
    align-items: center;
    z-index: 2;
    flex-shrink: 0;
}

/* Адаптивність для Edge з вертикальними вкладками */
@media (max-width: 1200px) {
    .searchBar {
        width: clamp(150px, 20vw, 250px);
    }

    .header_buttons_studies {
        gap: 16px;
    }
}

/* Для вузьких екранів (Edge з вертикальними вкладками) */
@media (max-width: 900px) {
    .header_studies {
        flex-wrap: wrap;
        gap: 12px;
    }

    .searchBar {
        width: clamp(120px, 18vw, 200px);
    }

    .header_buttons_studies {
        gap: 12px;
    }

    .header_buttons_studies button {
        padding: 12px 20px !important;
        font-size: 14px !important;
    }
}

/* Для дуже вузьких екранів */
@media (max-width: 600px) {
    .header_studies {
        flex-direction: column;
        padding: 16px 8px;
        gap: 16px;
    }

    .searchBar {
        width: 100%;
        max-width: 300px;
    }

    .header_buttons_studies {
        width: 100%;
        justify-content: center;
    }
}
