.container {
    display: flex;
    position: relative;
    flex-direction: column;
    padding-left: 84px;
    padding-right: 84px;
    height: 100vh;
    max-width: 100%;
    overflow: hidden;
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
    z-index: 0;
}

/* Декоративні лінії - позиції як на зображенні */
.line5 {
    position: absolute;
    left: clamp(200px, 25vw, 400px);
    bottom: clamp(150px, 25vh, 300px);
    z-index: 1;
    opacity: 0.9;
}

.line6 {
    position: absolute;
    right: clamp(100px, 15vw, 250px);
    bottom: clamp(80px, 12vh, 150px);
    z-index: 1;
    opacity: 0.9;
}

.line7 {
    position: absolute;
    left: clamp(300px, 35vw, 600px);
    top: clamp(200px, 30vh, 400px);
    z-index: 1;
    opacity: 0.9;
}

.line8 {
    position: absolute;
    left: clamp(150px, 20vw, 350px);
    top: clamp(50px, 8vh, 120px);
    z-index: 1;
    opacity: 0.9;
}

.line9 {
    position: absolute;
    right: clamp(200px, 30vw, 500px);
    top: clamp(80px, 12vh, 180px);
    z-index: 1;
    opacity: 0.9;
}

.line10 {
    position: absolute;
    right: clamp(50px, 8vw, 150px);
    top: clamp(300px, 45vh, 550px);
    z-index: 1;
    opacity: 0.9;
}

/* Приховуємо деякі декоративні елементи на малих екранах */
@media (max-width: 900px) {
    .line5, .line6, .line7 {
        display: none;
    }
}

@media (max-width: 600px) {
    .line8, .line9, .line10 {
        display: none;
    }
}

.header_studies {
    display: flex;
    width: 100%;
    max-width: 100vw;
    padding-top: 24px;
    padding-bottom: 24px;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.01);
    backdrop-filter: blur(4.449999809265137px);
    z-index: 2;
    box-sizing: border-box;
}

.logo {
    width: 50px;
    height: 50px;
}

.searchBar {
    display: flex;
    align-items: center;
    width: clamp(200px, 25vw, 326px);
    height: 55px;
    z-index: 2;
}

.notificationCenter {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.header_buttons_studies {
    display: flex;
    gap: clamp(12px, 2vw, 24px);
    color: #fff;
    justify-content: flex-end;
    align-items: center;
    z-index: 2;
    flex-shrink: 0;
}

.header_profile_dropdown {
    display: flex;
    align-items: center;
    z-index: 2;
    flex-shrink: 0;
}

/* Адаптивність для Edge з вертикальними вкладками */
@media (max-width: 1200px) {
    .searchBar {
        width: clamp(150px, 20vw, 250px);
    }

    .header_buttons_studies {
        gap: 16px;
    }
}

/* Для вузьких екранів (Edge з вертикальними вкладками) */
@media (max-width: 900px) {
    .header_studies {
        flex-wrap: wrap;
        gap: 12px;
    }

    .searchBar {
        width: clamp(120px, 18vw, 200px);
    }

    .header_buttons_studies {
        gap: 12px;
    }

    .header_buttons_studies button {
        padding: 12px 20px !important;
        font-size: 14px !important;
    }
}

/* Для дуже вузьких екранів */
@media (max-width: 600px) {
    .header_studies {
        flex-direction: column;
        padding: 16px 8px;
        gap: 16px;
    }

    .searchBar {
        width: 100%;
        max-width: 300px;
    }

    .header_buttons_studies {
        width: 100%;
        justify-content: center;
    }
}
