.container {
    display: flex;
    position: relative;
    flex-direction: column;
    padding-left: clamp(16px, 4vw, 64px);
    padding-right: clamp(16px, 4vw, 64px);
    height: 100vh;
    width: 100vw;
    max-width: 100%;
    overflow-x: hidden;
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
    z-index: 0;
}

.line5 {
    position: absolute;
    right: 1467px;
    bottom: 353px;
    z-index: 1;
}

.line6 {
    position: absolute;
    left: 1820px;
    bottom: 155.69px;
    z-index: 1;
}

.line7 {
    position: absolute;
    left: 1335px;
    top: 455px;
    z-index: 1;
}

.line8 {
    position: absolute;
    left: 1630px;
    z-index: 1;
}

.line9 {
    position: absolute;
    right: 1670px;
    z-index: 1;
}

.line10 {
    position: absolute;
    right: 1185.52px;
    top: 657px;
    z-index: 1;
}

.header_studies {
    display: flex;
    width: 1920px;
    padding-top: 24px;
    padding-bottom: 24px;
    justify-content: space-between;
    background: rgba(0, 0, 0, 0.01);
    backdrop-filter: blur(4.449999809265137px);
    z-index: 2;
}

.logo {
    width: 50px;
    height: 50px;
}

.searchBar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 326px;
    height: 55px;
    z-index: 2;
}

.notificationCenter {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.header_buttons_studies {
    display: flex;
    gap: 28px;
    color: #fff;
    justify-content: flex-end;
    align-items: center;
    z-index: 2;
}

.header_profile_dropdown {
    display: flex;
    align-items: center;
    z-index: 2;
}
