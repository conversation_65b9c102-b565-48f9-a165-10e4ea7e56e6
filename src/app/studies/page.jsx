'use client';

import SearchBar from '@/components/SearchBar';
import ProfileDropdown from '@/components/ProfileDropdown';
import NotificationCenter from '@/components/NotificationCenter';
import HeaderTestButton from '@/components/HeaderTestButton';
import AuthTestButton from '@/components/AuthTestButton';
import ResponsiveTestPanel from '@/components/ResponsiveTestPanel';
import StudiesSortDropdown from '@/components/StudiesSortDropdown';
import StudiesFilterDropdown from '@/components/StudiesFilterDropdown';
import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import styles from "./Studies.module.css";
import Link from "next/link";

export default function StudiesPage() {
    const { user, isAuthenticated, logout } = useAuth();
    const [showTestComponents, setShowTestComponents] = useState(false);
    const [sortBy, setSortBy] = useState('name_asc');
    const [filterBy, setFilterBy] = useState('all');

    const toggleTestComponents = () => {
        setShowTestComponents(!showTestComponents);
    };

    const handleSortChange = (newSort) => {
        setSortBy(newSort);
        console.log('Sort changed to:', newSort);
        // Тут буде логіка сортування студій
    };

    const handleFilterChange = (newFilter) => {
        setFilterBy(newFilter);
        console.log('Filter changed to:', newFilter);
        // Тут буде логіка фільтрування студій
    };

    useEffect(() => {
        const width = window.screen.width;
        const height = window.screen.height;

        fetch("/api/logScreenResolution", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ width, height }),
        });
    }, []);

    return (
        <div className={styles.container}>
            <img src="/Line 5.svg" className={styles.line5} />
            <img src="/Line 10.svg" className={styles.line10} />
            <img src="/Line 9.svg" className={styles.line9} />
            <header className={styles.header_studies}>
                <img src="/Group 1.svg" className={styles.logo} />
                <div style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    width: '100%',
                    gap: 'clamp(12px, 2vw, 24px)',
                    flexWrap: 'wrap'
                }}>
                    <div className={styles.searchBar}>
                        <SearchBar />
                    </div>
                    <div className={styles.notificationCenter}>
                        <NotificationCenter />
                    </div>
                    <HeaderTestButton
                        showTestComponents={showTestComponents}
                        toggleTestComponents={toggleTestComponents}
                    />
                    {isAuthenticated ? (
                        <div className={styles.header_profile_dropdown}>
                            <ProfileDropdown user={user} logout={logout} />
                        </div>
                    ) : (
                        <div className={styles.header_buttons_studies}>
                            <Link href="/signin">
                                <button className="header_button_login">
                                    Увійти
                                </button>
                            </Link>
                            <Link href="/signup">
                                <button className="header_button_signup">
                                    Реєстрація
                                </button>
                            </Link>
                        </div>
                    )}
                </div>
            </header>
            <div className={styles.studiesSection}>
                <h1 className={styles.studiesTitle}>Студії</h1>
                <img src="/Line 3_studies_pos_hedears.svg" className={styles.studiesUnderline} />
            </div>

            {/* Випадаючі списки під лінією */}
            <div className={styles.dropdownsContainer}>
                <StudiesSortDropdown
                    onSortChange={handleSortChange}
                    currentSort={sortBy}
                />
                <StudiesFilterDropdown
                    onFilterChange={handleFilterChange}
                    currentFilter={filterBy}
                />
            </div>

            <img src="/Line 6.svg" className={styles.line6} />
            <img src="/Line 8.svg" className={styles.line8} />
            <img src="/Line 7.svg" className={styles.line7} />

            {/* Тестові компоненти з'являються тільки при активації */}
            {showTestComponents && (
                <>
                    <AuthTestButton />
                    <ResponsiveTestPanel />
                </>
            )}
        </div>
    );
}