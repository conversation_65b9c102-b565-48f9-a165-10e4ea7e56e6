import SearchBar from '@/components/SearchBar';
import ProfileDropdown from '@/components/ProfileDropdown';
import NotificationCenter from '@/components/NotificationCenter';
import styles from "./Studies.module.css";
import Link from "next/link";

export default function StudiesPage() {
    return (
        <div className={styles.container}>
            <img src="/Line 5.svg" className={styles.line5} />
            <img src="/Line 10.svg" className={styles.line10} />
            <img src="/Line 9.svg" className={styles.line9} />
            <header className={styles.header_studies}>
                <img src="/Group 1.svg" className={styles.logo} />
                <div style={{ display: 'flex', alignItems: 'center', width: '100%', gap: '24px', marginRight: '110px' }}>
                    <div className={styles.searchBar}>
                        <SearchBar />
                    </div>
                    <div className={styles.notificationCenter}>
                        <NotificationCenter />
                    </div>
                    {/*<div className={styles.header_buttons_studies}>*/}
                    {/*    <Link href="/signin">*/}
                    {/*        <button*/}
                    {/*            className="header_button_login"*/}
                    {/*        >*/}
                    {/*            Увійти*/}
                    {/*        </button>*/}
                    {/*    </Link>*/}
                    {/*    <Link href="/signup">*/}
                    {/*        <button*/}
                    {/*            className="header_button_signup"*/}
                    {/*        >*/}
                    {/*            Реєстрація*/}
                    {/*        </button>*/}
                    {/*    </Link>*/}
                    {/*</div>*/}
                    <div className={styles.header_profile_dropdown}>
                        <ProfileDropdown />
                    </div>
                </div>
            </header>
            <img src="/Line 6.svg" className={styles.line6} />
            <img src="/Line 8.svg" className={styles.line8} />
            <img src="/Line 7.svg" className={styles.line7} />
        </div>
    );
}